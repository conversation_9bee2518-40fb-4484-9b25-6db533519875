#!/usr/bin/env python3
"""
Test script for the enhanced PDF processing with all fixes applied.
This script will process the clgdunia.pdf file with the improved functionality.
"""

import os
import sys
import logging
from pathlib import Path

# Add the qp_database directory to the path
sys.path.append(str(Path(__file__).parent / "qp_database"))

# Import the processing classes
from src.extractor import StartProcessing

def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pdf_processing.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main function to test the PDF processing."""
    print("🚀 Starting Enhanced PDF Processing Test")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Define paths
    current_dir = Path(__file__).parent
    input_dir = "input"  # This will be relative to qp_database folder
    excel_output_dir = "excel"
    images_dir = "excel/images"
    
    # Path to clgdunia.pdf (outside the folder)
    clgdunia_pdf_path = current_dir.parent / "clgdunia.pdf"
    
    print(f"📁 Looking for PDF at: {clgdunia_pdf_path}")
    
    if not clgdunia_pdf_path.exists():
        print(f"❌ Error: clgdunia.pdf not found at {clgdunia_pdf_path}")
        print("Please ensure the PDF file is in the correct location.")
        return False
    
    print(f"✅ Found PDF file: {clgdunia_pdf_path}")
    
    try:
        # Initialize processor
        print("🔧 Initializing PDF processor...")
        processor = StartProcessing(input_dir, excel_output_dir, images_dir)
        
        # Process the specific PDF
        print("📄 Starting PDF processing...")
        print("🎯 Processing first 20 pages for testing")
        print("🔍 Enhanced features:")
        print("   - Multi-page question support")
        print("   - Logo detection and avoidance")
        print("   - NEET-specific prompts")
        print("   - Improved image-question mapping")
        print("   - Better option extraction")
        
        success = processor.extract_all_questions_from_pdf(str(clgdunia_pdf_path))
        
        if success:
            print("\n🎉 Processing completed successfully!")
            print("📊 Check the excel folder for output files")
            print("🖼️  Check the excel/images folder for extracted images")
        else:
            print("\n❌ Processing failed. Check the logs for details.")
            
        return success
        
    except Exception as e:
        print(f"\n💥 Error during processing: {e}")
        logging.error(f"Processing error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
    
    input("\nPress Enter to exit...")
