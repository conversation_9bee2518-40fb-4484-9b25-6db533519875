# Enhanced NEET Question Paper Processing

This enhanced version of the PDF processing system includes several critical fixes and improvements for processing NEET question papers.

## 🚀 Key Improvements

### ✅ Fixed Issues
1. **Multi-page question options extraction** - Now correctly handles questions that span multiple pages
2. **Path handling** - Fixed to work with `clgdunia.pdf` located outside the folder
3. **NEET-specific prompts** - Generalized from physics-only to all NEET subjects (Physics, Chemistry, Biology)
4. **Logo detection** - Automatically detects and excludes logos in the top right corner
5. **Image-question mapping** - Enhanced accuracy in mapping images to correct questions

### 🔧 Technical Enhancements
- **Multi-page processing** - Groups pages to catch questions spanning multiple pages
- **Enhanced YOLO detection** - Better bounding box detection with logo filtering
- **Improved prompts** - NEET-specific examples for Physics, Chemistry, and Biology
- **Structured format** - Better JSON structure for questions and options
- **Enhanced image classification** - More accurate spatial awareness for image mapping

## 📁 File Structure
```
notcompleted/
├── qp_database/
│   ├── base.py              # Main processing logic (enhanced)
│   ├── src/
│   │   └── extractor.py     # Extraction interface (updated)
│   ├── yolo/
│   │   ├── best.pt          # YOLO model for image detection
│   │   └── data.yaml        # YOLO configuration
│   └── excel/
│       └── images/          # Output folder for extracted images
├── test_processing.py       # Test script to run processing
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🛠️ Setup Instructions

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   Create a `.env` file in the `qp_database` folder with:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

3. **Ensure PDF location:**
   Place `clgdunia.pdf` in the parent directory (outside the `notcompleted` folder)

## 🚀 Running the Processing

### Quick Test
```bash
cd notcompleted
python test_processing.py
```

### Manual Processing
```python
from qp_database.src.extractor import StartProcessing

# Initialize processor
processor = StartProcessing("input", "excel", "excel/images")

# Process specific PDF (first 20 pages)
processor.extract_all_questions_from_pdf("/path/to/clgdunia.pdf")
```

## 📊 Output

The system generates:
- **Excel file**: `excel/clgdunia_questions_integrated_latex.xlsx`
- **Images**: Extracted images in `excel/images/` folder
- **Logs**: Processing logs in `pdf_processing.log`

## 🔍 Features

### Multi-page Question Support
- Processes pages in groups to handle questions spanning multiple pages
- Ensures all 4 options (A, B, C, D) are captured even if they span pages

### Logo Detection
- Automatically detects logos in top right corner (15% width, 10% height)
- Filters out logo regions from image extraction

### NEET Subject Support
- **Physics**: Equations, formulas, diagrams
- **Chemistry**: Molecular formulas, reactions, structures
- **Biology**: Biological processes, anatomical diagrams, genetics

### Enhanced Image Mapping
- Spatial awareness for accurate question-image association
- Better option image detection and mapping
- Duplicate image prevention

## 🐛 Troubleshooting

1. **Missing API Key**: Ensure `GEMINI_API_KEY` is set in `.env` file
2. **PDF Not Found**: Check that `clgdunia.pdf` is in the correct location
3. **YOLO Model**: Ensure `best.pt` model file exists in `yolo/` folder
4. **Dependencies**: Run `pip install -r requirements.txt`

## 📝 Notes

- Processing is limited to first 20 pages for testing
- Images are saved with descriptive filenames including PDF name, question number, and type
- LaTeX mathematical content is integrated directly into text fields
- Both English and Tamil versions of questions are extracted
