import os
import time
import sys
from pathlib import Path

# Add parent directory to path to import base module
sys.path.append(str(Path(__file__).parent.parent))
from base import PDFProcessor
import fitz


class StartProcessing(PDFProcessor):

    def __init__(self, input_dir, excel_output_dir, images_dir):
        self.input_dir = input_dir
        self.excel_output_dir = excel_output_dir
        self.images_dir = images_dir

        # Create separate folder for question-by-question results
        self.question_by_question_dir = "question_by_question_results"
        self.qbq_excel_dir = os.path.join(self.question_by_question_dir, "excel")
        self.qbq_images_dir = os.path.join(self.question_by_question_dir, "images")

        # Create directories if they don't exist
        os.makedirs(self.qbq_excel_dir, exist_ok=True)
        os.makedirs(self.qbq_images_dir, exist_ok=True)

    def extract_all_questions_from_pdf(self, specific_pdf_path=None):
        """Extract content from question paper with question-by-question approach."""
        start_time = time.time()

        # Initialize PDFProcessor with question-by-question directories
        super().__init__(
            self.input_dir,
            self.qbq_excel_dir,  # Use separate folder
            self.qbq_images_dir  # Use separate folder
        )

        if specific_pdf_path:
            # Process specific PDF file (like clgdunia.pdf)
            pdf_files = [specific_pdf_path]
            print(f"🎯 Processing specific PDF: {specific_pdf_path}")
            print(f"📁 Results will be saved in: {self.question_by_question_dir}")
        else:
            # List all PDF files in input_dir
            pdf_files = [os.path.join(self.input_dir, f) for f in os.listdir(self.input_dir) if f.lower().endswith(".pdf")]

        if not pdf_files:
            print("❌ No PDF files found.")
            return False

        # Check API key before processing
        if not self.check_api_keys():
            print("❌ API key issues detected. Please check your configuration.")
            return False

        for pdf_path in pdf_files:
            print(f"📄 Processing {pdf_path}...")

            # Get total number of pages in the PDF
            try:
                doc = fitz.open(pdf_path)
                total_pages = len(doc)
                doc.close()

                # Process first 20 pages (from 1 to 20) for testing
                end_page = min(20, total_pages)  # Limit to first 20 pages or total pages if less than 20
                print(f"📊 PDF has {total_pages} pages, processing pages 1 to {end_page} (first 20 pages only)")
                print(f"🔄 Using simple page-by-page approach")

                success = self.process_pdf(pdf_path, start_page=1, end_page=end_page)

            except Exception as e:
                print(f"❌ Error reading PDF {pdf_path}: {e}")
                success = False

            if success:
                print(f"✅ Successfully processed: {os.path.basename(pdf_path)}")
                print(f"📁 Results saved in: {self.question_by_question_dir}")
            else:
                print(f"❌ Failed to process: {os.path.basename(pdf_path)}")

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️  Total execution time: {execution_time:.2f} seconds")
        return success

    def check_api_keys(self):
        """Check if required API keys are available."""
        try:
            # Check Gemini API key
            import os
            from dotenv import load_dotenv
            load_dotenv()

            gemini_key = os.getenv('GEMINI_API_KEY')
            openai_key = os.getenv('OPENAI_API_KEY')

            if not gemini_key:
                print("❌ GEMINI_API_KEY not found in environment variables")
                print("💡 Please add GEMINI_API_KEY to your .env file")
                return False

            if not openai_key:
                print("⚠️  OPENAI_API_KEY not found - some features may not work")
                print("💡 Please add OPENAI_API_KEY to your .env file for full functionality")
                # Continue without OpenAI for now

            print("✅ API keys checked successfully")
            return True

        except Exception as e:
            print(f"❌ Error checking API keys: {e}")
            return False


# Main execution function for testing
def main():
    """Main function to run the PDF processing."""
    # Define paths
    current_dir = Path(__file__).parent.parent
    input_dir = "input"  # This will be relative to qp_database folder
    excel_output_dir = "excel"
    images_dir = "excel/images"

    # Path to clgdunia.pdf (outside the folder)
    clgdunia_pdf_path = current_dir.parent.parent / "clgdunia.pdf"

    if not clgdunia_pdf_path.exists():
        print(f"Error: clgdunia.pdf not found at {clgdunia_pdf_path}")
        return

    # Initialize processor
    processor = StartProcessing(input_dir, excel_output_dir, images_dir)

    # Process the specific PDF
    processor.extract_all_questions_from_pdf(str(clgdunia_pdf_path))


if __name__ == "__main__":
    main()


