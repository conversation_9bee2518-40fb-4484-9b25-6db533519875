import os
import time
import sys
from pathlib import Path

# Add parent directory to path to import base module
sys.path.append(str(Path(__file__).parent.parent))
from base import PDFProcessor
import fitz


class StartProcessing(PDFProcessor):

    def __init__(self, input_dir, excel_output_dir, images_dir):
        self.input_dir = input_dir
        self.excel_output_dir = excel_output_dir
        self.images_dir = images_dir

    def extract_all_questions_from_pdf(self, specific_pdf_path=None):
        """Extract content from question paper with option to specify a specific PDF."""
        start_time = time.time()

        # Initialize PDFProcessor properly
        super().__init__(
            self.input_dir,
            self.excel_output_dir,
            self.images_dir
        )

        if specific_pdf_path:
            # Process specific PDF file (like clgdunia.pdf)
            pdf_files = [specific_pdf_path]
            print(f"Processing specific PDF: {specific_pdf_path}")
        else:
            # List all PDF files in input_dir
            pdf_files = [os.path.join(self.input_dir, f) for f in os.listdir(self.input_dir) if f.lower().endswith(".pdf")]

        if not pdf_files:
            print("No PDF files found.")
            return

        for pdf_path in pdf_files:
            print(f"Processing {pdf_path}...")

            # Get total number of pages in the PDF
            try:
                doc = fitz.open(pdf_path)
                total_pages = len(doc)
                doc.close()

                # Process first 20 pages (from 1 to 20) for testing
                end_page = min(20, total_pages)  # Limit to first 20 pages or total pages if less than 20
                print(f"📄 PDF has {total_pages} pages, processing pages 1 to {end_page} (first 20 pages only)")
                success = self.process_pdf(pdf_path, start_page=1, end_page=end_page)
            except Exception as e:
                print(f"Error reading PDF {pdf_path}: {e}")
                success = False

            if success:
                print(f"✅ Successfully processed: {os.path.basename(pdf_path)}")
            else:
                print(f"❌ Failed to process: {os.path.basename(pdf_path)}")

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Execution time for question extraction: {execution_time:.2f} seconds")


# Main execution function for testing
def main():
    """Main function to run the PDF processing."""
    # Define paths
    current_dir = Path(__file__).parent.parent
    input_dir = "input"  # This will be relative to qp_database folder
    excel_output_dir = "excel"
    images_dir = "excel/images"

    # Path to clgdunia.pdf (outside the folder)
    clgdunia_pdf_path = current_dir.parent.parent / "clgdunia.pdf"

    if not clgdunia_pdf_path.exists():
        print(f"Error: clgdunia.pdf not found at {clgdunia_pdf_path}")
        return

    # Initialize processor
    processor = StartProcessing(input_dir, excel_output_dir, images_dir)

    # Process the specific PDF
    processor.extract_all_questions_from_pdf(str(clgdunia_pdf_path))


if __name__ == "__main__":
    main()


